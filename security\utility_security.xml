<?xml version="1.0" encoding="utf-8"?>

<odoo>
    <data noupdate="1">
        <!-- Module Category -->
        <record id="aruc_module" model="ir.module.category">
            <field name="name">Utility Consumption</field>
            <field name="description">Utility Consumption System</field>
            <field name="sequence">999</field>
        </record>

        <!-- User Group -->
        <record id="group_aruc_user" model="res.groups">
            <field name="name">User</field>
            <field name="category_id" ref="aruc_module"/>
            <field name="implied_ids" eval="[(4, ref('base.group_user'))]"/>
        </record>

        <!-- Manager Group -->
        <record id="group_aruc_manager" model="res.groups">
            <field name="name">Manager</field>
            <field name="category_id" ref="aruc_module"/>
            <field name="implied_ids" eval="[(4, ref('ar_utility_consumption.group_aruc_user'))]"/>
            <field name="users" eval="[(4, ref('base.user_admin'))]"/>
        </record>

    </data>
</odoo>
