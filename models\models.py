# -*- coding: utf-8 -*-

from odoo import models, fields, api

class UtilityType(models.Model):
    _name = 'utility.type'
    _description = 'Utility Type'
    _inherit = ['mail.thread']

    name = fields.Char(
        string='Utility Name', 
        required=True,
        tracking=True
    )

    uom_id = fields.Many2one(
        comodel_name='uom.uom', 
        string='UoM', 
        required=True,
        tracking=True
    )

    active = fields.Boolean(
        string='Active',
        default=True,
        tracking=True
    )

    _sql_constraints = [
        (
            'unique_utility_type_name', 
            'unique(name)', 
            'Utility type name must be unique!'
        )
    ]

class UtilityConsumption(models.Model):
    _name = 'utility.consumption'
    _description = 'Utility Consumption'
    _rec_name = 'date_recorded'
    _order = "date_recorded desc"
    _inherit = ['mail.thread']

    date_recorded = fields.Date(
        string='Date', 
        required=True, 
        default=fields.Date.today(),
        copy=False,
        tracking=True,
        states={
            'draft': [('readonly', False)], 
            'confirm': [('readonly', True)]
        }
    )

    utility_consumption_ids = fields.One2many(
        comodel_name='utility.consumption.line',
        inverse_name='utility_consumption_id',
        readonly=False, 
        copy=True,
        states={
            'draft': [('readonly', False)], 
            'confirm': [('readonly', True)]
        }
    )

    total_electricity = fields.Float(
        string='Total Electricity',
        compute='_compute_totals'
    )

    total_gas = fields.Float(
        string='Total Gas',
        compute='_compute_totals'
    )

    total_water_1 = fields.Float(
        string='Total Water (KWSB)',
        compute='_compute_totals'
    )

    total_water_2 = fields.Float(
        string='Total Water (KCIP)',
        compute='_compute_totals'
    )

    total_others = fields.Float(
        string='Total Others',
        compute='_compute_totals'
    )

    notes = fields.Char(
        string='Notes',
        size=50, 
        copy=False,
        states={
            'draft': [('readonly', False)], 
            'confirm': [('readonly', True)]
        }
    )

    state = fields.Selection([
	        ('draft', 'Draft'),
	        ('confirm', 'Confirmed')
        ], 
        string='Status', 
        default='draft', 
        copy=False,
        readonly=True,
        tracking=True
    )

    _sql_constraints = [
        (
            'unique_date_recorded', 
            'unique(date_recorded)', 
            'Consumption for this utility date already exists!'
        )
    ]

    def action_confirm(self):
        self.state = 'confirm'

    def action_reset(self):
        self.state = 'draft'

    @api.depends('utility_consumption_ids.unit_consumed', 'utility_consumption_ids.utility_type_id')
    def _compute_totals(self):
        for rec in self:
            rec.total_electricity = sum(
                line.unit_consumed for line in rec.utility_consumption_ids
                if line.utility_type_id and line.utility_type_id.name.lower() == 'electricity'
            )

            rec.total_gas = sum(
                line.unit_consumed for line in rec.utility_consumption_ids
                if line.utility_type_id and line.utility_type_id.name.lower() == 'gas'
            )

            rec.total_water_1 = sum(
                line.unit_consumed for line in rec.utility_consumption_ids
                if line.utility_type_id and line.utility_type_id.name.lower() == 'water (kwsb)'
            )

            rec.total_water_2 = sum(
                line.unit_consumed for line in rec.utility_consumption_ids
                if line.utility_type_id and line.utility_type_id.name.lower() == 'water (kcip)'
            )

            rec.total_others = sum(
                line.unit_consumed for line in rec.utility_consumption_ids
                if line.utility_type_id and line.utility_type_id.name.lower() not in ['electricity', 'gas', 'water (kwsb)', 'water (kcip)']
            )


class UtilityConsumptionLine(models.Model):
    _name = 'utility.consumption.line'
    _description = 'Utility Consumption Line'
    _inherit = ['mail.thread']

    utility_consumption_id = fields.Many2one(
        comodel_name='utility.consumption', 
        string='Utility Consumption Record', 
        ondelete='cascade'
    )

    department_id = fields.Many2one(
        comodel_name='hr.department', 
        string='Department', 
        required=True,
        tracking=True
    )

    utility_type_id = fields.Many2one(
        comodel_name='utility.type',
        string='Utility Type',
        required=True,
        tracking=True
    )

    uom_name = fields.Char(
        string='UOM',
        compute='_compute_uom_name',
        store=False
    )

    unit_consumed = fields.Float(
        string='Units Consumed', 
        required=True,
        tracking=True
    )

    _sql_constraints = [
        (
            'unique_line_per_dept_type_date',
            'unique(utility_consumption_id, department_id, utility_type_id)',
            'A line for this department and utility type already exists for this date!'
        )
    ]
    
    @api.depends('utility_type_id')
    def _compute_uom_name(self):
        for rec in self:
            rec.uom_name = ''
            # if rec.utility_type_id and rec.utility_type_id.uom_id:
            #     rec.uom_name = rec.utility_type_id.uom_id.name or ''

            if rec.utility_type_id and rec.utility_type_id.uom_id:
                rec.uom_name = rec.utility_type_id.uom_id.name or ''
            else:
                rec.uom_name = ''

    @api.model
    def create(self, vals):
        record = super(UtilityConsumptionLine, self).create(vals)
        if record.utility_consumption_id:
            record.utility_consumption_id.message_post(
                # body=f"A new utility consumption line has been added:<br/>"
                #      f"Department: {record.department_id.name}<br/>"
                #      f"Utility Type: {record.utility_type_id.name}<br/>"
                #      f"Units Consumed: {record.unit_consumed} {record.uom_name}",

                body=f"A new utility consumption line has been added:<br/>"
                     f"Department: {record.department_id.name}<br/>"
                     f"Utility Type: {record.utility_type_id.name}<br/>"
                     f"Units Consumed: {record.unit_consumed}",
                subtype_xmlid='mail.mt_note' # Or mail.mt_comment
            )

        return record

    def write(self, vals):
        # Store old values for comparison in the message
        old_values = {
            line.id: {
                'department_id': line.department_id.name,
                'utility_type_id': line.utility_type_id.name,
                'unit_consumed': line.unit_consumed,
                # 'uom_name': line.uom_name,
            } for line in self
        }

        res = super(UtilityConsumptionLine, self).write(vals)

        for record in self:
            if record.utility_consumption_id:
                message_body_parts = []
                message_body_parts.append(f"A utility consumption line has been updated for {old_values[record.id]['department_id']}:<br/>")

                if 'department_id' in vals and old_values[record.id]['department_id'] != record.department_id.name:
                    message_body_parts.append(f"Department changed from {old_values[record.id]['department_id']} to {record.department_id.name}<br/>")

                if 'utility_type_id' in vals and old_values[record.id]['utility_type_id'] != record.utility_type_id.name:
                    message_body_parts.append(f"Utility Type changed from {old_values[record.id]['utility_type_id']} to {record.utility_type_id.name}<br/>")

                if 'unit_consumed' in vals and old_values[record.id]['unit_consumed'] != record.unit_consumed:
                    # message_body_parts.append(f"Units Consumed changed from {old_values[record.id]['unit_consumed']} {old_values[record.id]['uom_name']} to {record.unit_consumed} {record.uom_name}<br/>")
                    message_body_parts.append(f"Units Consumed changed from {old_values[record.id]['unit_consumed']} to {record.unit_consumed}<br/>")

                if len(message_body_parts) > 1: # Only post if there were actual changes
                     record.utility_consumption_id.message_post(
                        body="".join(message_body_parts),
                        subtype_xmlid='mail.mt_note'
                    )
        return res

    def unlink(self):
        # Before deleting, capture information to post on the parent
        lines_info = []
        for record in self:
            if record.utility_consumption_id:
                lines_info.append({
                    'parent_id': record.utility_consumption_id,
                    'department': record.department_id.name,
                    'utility_type': record.utility_type_id.name,
                    'units_consumed': record.unit_consumed,
                    # 'uom_name': record.uom_name,
                })

        res = super(UtilityConsumptionLine, self).unlink()

        for info in lines_info:
            info['parent_id'].message_post(
                # body=f"A utility consumption line has been deleted:<br/>"
                #      f"Department: {info['department']}<br/>"
                #      f"Utility Type: {info['utility_type']}<br/>"
                #      f"Units Consumed: {info['units_consumed']} {info['uom_name']}",

                body=f"A utility consumption line has been deleted:<br/>"
                     f"Department: {info['department']}<br/>"
                     f"Utility Type: {info['utility_type']}<br/>"
                     f"Units Consumed: {info['units_consumed']}",
                subtype_xmlid='mail.mt_note'
            )

        return res