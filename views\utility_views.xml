<?xml version="1.0" encoding="utf-8"?>

<odoo>
    <data>
        <!-- Utility Type Tree View -->
        <record id="utility_type_tree_view" model="ir.ui.view">
            <field name="name">utility.type.tree</field>
            <field name="model">utility.type</field>
            <field name="arch" type="xml">
                <tree string="Utility Type">
                    <field name="name"/>
                    <field name="uom_id"/>
                    <field name="active" widget="boolean_toggle"/>
                </tree>
            </field>
        </record>

        <!-- Utility Type Form View -->
        <record id="utility_type_form_view" model="ir.ui.view">
            <field name="name">utility.type.form</field>
            <field name="model">utility.type</field>
            <field name="arch" type="xml">
                <form string="Utility Type">
                    <sheet>
                        <group>
                            <field name="name"/>
                            <field name="uom_id" options="{'no_create': True, 'no_edit': True}"/>
                            <field name="active" widget="boolean_toggle"/>
                        </group>
                    </sheet>
                    <div class="oe_chatter">
                        <field name="message_follower_ids" widget="mail_followers"/>
                        <field name="message_ids" widget="mail_thread"/>
                    </div>
                </form>
            </field>
        </record>

        <!-- Action for Utility Type -->
        <record id="utility_type_action" model="ir.actions.act_window">
            <field name="name">Utility Types</field>
            <field name="res_model">utility.type</field>
            <field name="view_mode">tree,form</field>
        </record>

        <!-- Menu Item for Utility Module -->
        <menuitem 
            name="Utility" 
            id="menu_utility_module" 
            parent="data_entry.menu_root"
            groups="ar_utility_consumption.group_aruc_user,ar_utility_consumption.group_aruc_manager" 
            sequence="10"
        />

        <!-- Menu Item for Utility Type -->
        <menuitem 
            id="menu_utility_type"
            name="Utility Type"
            parent="menu_utility_module"
            action="utility_type_action"
            groups="ar_utility_consumption.group_aruc_user,ar_utility_consumption.group_aruc_manager" 
            sequence="10"
        />

        <!-- Utility Consumption Form (master/detail) -->
        <record id="utility_consumption_form_view" model="ir.ui.view">
            <field name="name">utility.consumption.form</field>
            <field name="model">utility.consumption</field>
            <field name="arch" type="xml">
                <form string="Utility Consumption">
                    <header>
                        <button 
                            name="action_confirm"
                            string="Confirm" 
                            type="object"
                            class="oe_highlight" 
                            attrs="{'invisible': [('state', 'not in', ['draft'])]}"
                            groups="ar_utility_consumption.group_aruc_manager"
                        />
                        <button 
                            name="action_reset" 
                            string="Reset" 
                            type="object"
                            class="oe_highlight"
                            attrs="{'invisible': [('state', '=', 'draft')]}" 
                            groups="ar_utility_consumption.group_aruc_manager"
                        />
                        <field name="state" widget="statusbar"/>
                        <!-- <field name="state" widget="statusbar" options="{'clickable': '1'}"/> -->
                    </header>
                    <sheet>
                        <group col="4" colspan="4">
                            <field name="date_recorded"/>
                            <field name="notes"/>
                        </group>
                        <group string="Totals by Utility Type:" col="10">
                            <field name="total_electricity" string="Electricity" readonly="1"/>
                            <field name="total_gas" string="Gas" readonly="1"/>
                            <field name="total_water_1" string="Water (KWSB)" readonly="1"/>
                            <field name="total_water_2" string="Water (KCIP)" readonly="1"/>
                            <field name="total_others" string="Others" readonly="1"/>
                        </group>
                        <notebook>
                            <page string="Consumption Lines">
                                <!-- <field name="utility_consumption_ids" mode="tree,form" context="{'default_date_recorded': date_recorded}"> -->
                                <field name="utility_consumption_ids" nolabel="1">
                                    <tree editable="bottom" class="o_list_table_compact" limit="10">
                                        <field name="department_id"
                                               options="{'no_create': True, 'no_edit': True}"
                                               string="Dept"
                                               width="30%"/>
                                        <field name="utility_type_id"
                                               options="{'no_create': True, 'no_edit': True}"
                                               string="Utility"
                                               width="35%"/>
                                        <field name="unit_consumed"
                                               sum="Total Units Consumed"
                                               string="Units"
                                               width="25%"/>
                                        <field name="uom_name"
                                               readonly="1"
                                               string="UoM"
                                               width="10%"
                                               optional="hide"/>
                                    </tree>
                                </field>
                            </page>
                        </notebook>
                    </sheet>
                    <div class="oe_chatter">
                        <field name="message_follower_ids" widget="mail_followers"/>
                        <field name="message_ids" widget="mail_thread"/>
                    </div>
                </form>
            </field>
        </record>

        <!-- Utility Consumption Line Form View -->
        <record id="utility_consumption_line_form_view" model="ir.ui.view">
            <field name="name">utility.consumption.line.form</field>
            <field name="model">utility.consumption.line</field>
            <field name="arch" type="xml">
                <form string="Utility Consumption Line">
                    <sheet>
                        <group>
                            <field name="utility_consumption_id"/>
                            <field name="department_id" options="{'no_create': True, 'no_edit': True}"/>
                            <field name="utility_type_id" options="{'no_create': True, 'no_edit': True}"/>
                            <field name="uom_name" readonly="1"/>
                            <field name="unit_consumed"/>
                        </group>
                    </sheet>
                    <div class="oe_chatter">
                        <field name="message_follower_ids" widget="mail_followers"/>
                        <field name="message_ids" widget="mail_thread"/>
                    </div>
                </form>
            </field>
        </record>

        <!-- Utility Consumption Tree View -->
        <record id="utility_consumption_tree_view" model="ir.ui.view">
            <field name="name">utility.consumption.tree</field>
            <field name="model">utility.consumption</field>
            <field name="arch" type="xml">
                <tree string="Utility Consumptions">
                    <field name="date_recorded"/>
                    <field name="notes" optional="show"/>
                    <field name="total_electricity" string="Electricity" optional="show" sum="Total Electricity"/>
                    <field name="total_gas" string="Gas" optional="show" sum="Total Gas"/>
                    <field name="total_water_1" string="Water (KWSB)" optional="show" sum="Total Water (KWSB)"/>
                    <field name="total_water_2" string="Water (KCIP)" optional="show" sum="Total Water (KCIP)"/>
                    <field name="total_others" string="Others" optional="show" sum="Total Others"/>
                    <field name="utility_consumption_ids" string="Lines" optional="show"/>
                    <field 
                        name="state" 
                        readonly="1" 
                        widget="badge" 
                        decoration-success="state == 'confirmed'" 
                        decoration-primary="state == 'draft'"
                    />
                </tree>
            </field>
        </record>

        <!-- Action for Utility Consumption -->
        <record id="utility_consumption_action" model="ir.actions.act_window">
            <field name="name">Utility Consumptions</field>
            <field name="res_model">utility.consumption</field>
            <field name="view_mode">tree,form</field>
        </record>

        <!-- Menu Item for Utility Consumption -->
        <menuitem 
            id="menu_utility_consumption"
            name="Utility Consumption"
            parent="menu_utility_module"
            action="utility_consumption_action"
            groups="ar_utility_consumption.group_aruc_user,ar_utility_consumption.group_aruc_manager" 
            sequence="20"
        />

    </data>
</odoo>