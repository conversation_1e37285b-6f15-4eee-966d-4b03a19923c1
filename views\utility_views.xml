<?xml version="1.0" encoding="utf-8"?>

<odoo>
    <data>
        <!-- Utility Type Tree View -->
        <record id="utility_type_tree_view" model="ir.ui.view">
            <field name="name">utility.type.tree</field>
            <field name="model">utility.type</field>
            <field name="arch" type="xml">
                <tree string="Utility Type">
                    <field name="name"/>
                    <field name="uom_id"/>
                    <field name="active" widget="boolean_toggle"/>
                </tree>
            </field>
        </record>

        <!-- Utility Type Form View -->
        <record id="utility_type_form_view" model="ir.ui.view">
            <field name="name">utility.type.form</field>
            <field name="model">utility.type</field>
            <field name="arch" type="xml">
                <form string="Utility Type">
                    <sheet>
                        <group>
                            <field name="name"/>
                            <field name="uom_id" options="{'no_create': True, 'no_edit': True}"/>
                            <field name="active" widget="boolean_toggle"/>
                        </group>
                    </sheet>
                    <div class="oe_chatter">
                        <field name="message_follower_ids" widget="mail_followers"/>
                        <field name="message_ids" widget="mail_thread"/>
                    </div>
                </form>
            </field>
        </record>

        <!-- Action for Utility Type -->
        <record id="utility_type_action" model="ir.actions.act_window">
            <field name="name">Utility Types</field>
            <field name="res_model">utility.type</field>
            <field name="view_mode">tree,form</field>
        </record>

        <!-- Menu Item for Utility Module -->
        <menuitem 
            name="Utility" 
            id="menu_utility_module" 
            parent="data_entry.menu_root"
            groups="ar_utility_consumption.group_aruc_user,ar_utility_consumption.group_aruc_manager" 
            sequence="10"
        />

        <!-- Menu Item for Utility Type -->
        <menuitem 
            id="menu_utility_type"
            name="Utility Type"
            parent="menu_utility_module"
            action="utility_type_action"
            groups="ar_utility_consumption.group_aruc_user,ar_utility_consumption.group_aruc_manager" 
            sequence="10"
        />

        <!-- Utility Consumption Form (master/detail) -->
        <record id="utility_consumption_form_view" model="ir.ui.view">
            <field name="name">utility.consumption.form</field>
            <field name="model">utility.consumption</field>
            <field name="arch" type="xml">
                <form string="Utility Consumption">
                    <header>
                        <button 
                            name="action_confirm"
                            string="Confirm" 
                            type="object"
                            class="oe_highlight" 
                            attrs="{'invisible': [('state', 'not in', ['draft'])]}"
                            groups="ar_utility_consumption.group_aruc_manager"
                        />
                        <button 
                            name="action_reset" 
                            string="Reset" 
                            type="object"
                            class="oe_highlight"
                            attrs="{'invisible': [('state', '=', 'draft')]}" 
                            groups="ar_utility_consumption.group_aruc_manager"
                        />
                        <field name="state" widget="statusbar"/>
                        <!-- <field name="state" widget="statusbar" options="{'clickable': '1'}"/> -->
                    </header>
                    <sheet>
                        <group col="4" colspan="4">
                            <field name="date_recorded"/>
                            <field name="notes"/>
                        </group>
                        <group string="Totals by Utility Type:" col="10">
                            <field name="total_electricity" string="Electricity" readonly="1"/>
                            <field name="total_gas" string="Gas" readonly="1"/>
                            <field name="total_water_1" string="Water (KWSB)" readonly="1"/>
                            <field name="total_water_2" string="Water (KCIP)" readonly="1"/>
                            <field name="total_others" string="Others" readonly="1"/>
                        </group>
                        <notebook>
                            <page string="Consumption Lines">
                                <!-- <field name="utility_consumption_ids" mode="tree,form" context="{'default_date_recorded': date_recorded}"> -->
                                <field name="utility_consumption_ids">
                                    <tree editable="bottom">
                                        <field name="department_id" options="{'no_create': True, 'no_edit': True}"/>
                                        <field name="utility_type_id" options="{'no_create': True, 'no_edit': True}"/>
                                        <field name="uom_name" readonly="1"/>
                                        <field name="unit_consumed" sum="Total Units Consumed"/>
                                    </tree>
                                </field>
                            </page>
                        </notebook>
                    </sheet>
                    <div class="oe_chatter">
                        <field name="message_follower_ids" widget="mail_followers"/>
                        <field name="message_ids" widget="mail_thread"/>
                    </div>
                </form>
            </field>
        </record>

        <!-- Utility Consumption Line Form View -->
        <record id="utility_consumption_line_form_view" model="ir.ui.view">
            <field name="name">utility.consumption.line.form</field>
            <field name="model">utility.consumption.line</field>
            <field name="arch" type="xml">
                <form string="Utility Consumption Line">
                    <sheet>
                        <group>
                            <field name="utility_consumption_id"/>
                            <field name="department_id" options="{'no_create': True, 'no_edit': True}"/>
                            <field name="utility_type_id" options="{'no_create': True, 'no_edit': True}"/>
                            <field name="uom_name" readonly="1"/>
                            <field name="unit_consumed"/>
                        </group>
                    </sheet>
                    <div class="oe_chatter">
                        <field name="message_follower_ids" widget="mail_followers"/>
                        <field name="message_ids" widget="mail_thread"/>
                    </div>
                </form>
            </field>
        </record>

        <!-- Utility Consumption Tree View -->
        <record id="utility_consumption_tree_view" model="ir.ui.view">
            <field name="name">utility.consumption.tree</field>
            <field name="model">utility.consumption</field>
            <field name="arch" type="xml">
                <tree string="Utility Consumptions">
                    <field name="date_recorded"/>
                    <field name="notes" optional="show"/>
                    <field name="total_electricity" string="Electricity" optional="show" sum="Total Electricity"/>
                    <field name="total_gas" string="Gas" optional="show" sum="Total Gas"/>
                    <field name="total_water_1" string="Water (KWSB)" optional="show" sum="Total Water (KWSB)"/>
                    <field name="total_water_2" string="Water (KCIP)" optional="show" sum="Total Water (KCIP)"/>
                    <field name="total_others" string="Others" optional="show" sum="Total Others"/>
                    <field name="utility_consumption_ids" string="Lines" optional="show"/>
                    <field 
                        name="state" 
                        readonly="1" 
                        widget="badge" 
                        decoration-success="state == 'confirmed'" 
                        decoration-primary="state == 'draft'"
                    />
                </tree>
            </field>
        </record>


        <record id="emb_costing_search_view" model="ir.ui.view">
            <field name="name">emb.costing.search.view</field>
            <field name="model">emb.costing</field>
            <field name="arch" type="xml">
                <search string="Search Costing">
                    <field name="name" string="Design" filter_domain="[('name', 'ilike', self)]"/>
                    <field name="product_id"/>
                    <field name="partner_id" operator="child_of"/>
                    <filter name="draft" string="Draft" domain="[('state', '=', 'draft')]"/>
                    <separator/>
                    <filter name="confirmed" string="Confirmed" domain="[('state', '=', 'confirmed')]"/>
                    <separator/>
                    <filter name="cancelled" string="Cancelled" domain="[('state', '=', 'cancelled')]"/>
                    <group expand="0" string="Group By">
                        <filter string="Product" name="product" domain="[]" context="{'group_by': 'product_id'}"/>
                        <filter string="Customer" name="customer" domain="[]" context="{'group_by': 'partner_id'}"/>
                        <filter string="State" name="state" domain="[]" context="{'group_by': 'state'}"/>
                    </group>
                </search>
            </field>
        </record>

        <!-- Utility Consumption Line Pivot View -->
        <record id="utility_consumption_line_pivot_view" model="ir.ui.view">
            <field name="name">utility.consumption.line.pivot</field>
            <field name="model">utility.consumption.line</field>
            <field name="arch" type="xml">
                <pivot string="Detailed Utility Consumption Analysis">
                    <field name="date_recorded" type="row" interval="month"/>
                    <field name="department_id" type="col"/>
                    <field name="utility_type_id" type="row"/>
                    <field name="state" type="filter"/>
                    <field name="unit_consumed" type="measure"/>
                </pivot>
            </field>
        </record>

        <!-- Action for Utility Consumption -->
        <record id="utility_consumption_action" model="ir.actions.act_window">
            <field name="name">Utility Consumptions</field>
            <field name="res_model">utility.consumption</field>
            <field name="view_mode">tree,form</field>
        </record>

        <!-- Action for Utility Consumption Analysis -->
        <record id="utility_consumption_line_analysis_action" model="ir.actions.act_window">
            <field name="name">Utility Consumption Analysis</field>
            <field name="res_model">utility.consumption.line</field>
            <field name="view_mode">pivot</field>
            <field name="view_id" ref="utility_consumption_line_pivot_view"/>
            <!-- <field name="domain">[('state', '=', 'confirm')]</field> -->
        </record>

        <!-- Menu Item for Utility Consumption -->
        <menuitem 
            id="menu_utility_consumption"
            name="Utility Consumption"
            parent="menu_utility_module"
            action="utility_consumption_action"
            groups="ar_utility_consumption.group_aruc_user,ar_utility_consumption.group_aruc_manager" 
            sequence="20"
        />

        <!-- Menu Item for Utility Consumption Analysis -->
        <menuitem 
            id="menu_utility_analysis"
            name="Consumption Analysis"
            parent="menu_utility_module"
            action="utility_consumption_line_analysis_action"
            groups="ar_utility_consumption.group_aruc_user,ar_utility_consumption.group_aruc_manager" 
            sequence="30"
        />
    </data>
</odoo>